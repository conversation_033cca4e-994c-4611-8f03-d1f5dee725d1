<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Purchase;
use Illuminate\Http\Request;

class PurchaseController extends Controller {
    public function buy(Product $product)
{
    $user = auth()->user();

    if ($user->credit < $product->price) {
        return view('errors.insufficient_credit');
    }

    if ($product->stock <= 0) {
        return back()->withErrors('Sorry, this product is out of stock!');
    }

    $user->credit -= $product->price;
    $user->save();

    $product->stock -= 1;
    $product->save();

    Purchase::create([
        'user_id' => $user->id,
        'product_id' => $product->id
    ]);

    return back()->with('success', 'Product purchased successfully!');
}
public function index()
{
    $purchases = auth()->user()->purchases ?? [];
    return view('purchases.index', compact('purchases'));
}
public function product()
{
    return $this->belongsTo(Product::class);
}
public function return(Purchase $purchase)
{
    $user = auth()->user();

    if ($purchase->user_id !== $user->id) {
        abort(403, 'Unauthorized');
    }

    $user->credit += $purchase->price;
    $user->save();

    if ($purchase->product) {
        $purchase->product->stock += 1;
        $purchase->product->save();
    }

    $purchase->delete();

    return redirect()->back()->with('success', 'Product returned successfully and credit refunded.');
}

}