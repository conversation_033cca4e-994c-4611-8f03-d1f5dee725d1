@extends('layouts.app')

@section('content')
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">

            <div class="card shadow-sm border-0">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">My Profile</h4>
                    <a href="{{ route('home') }}" class="btn btn-sm btn-light">Back to Home</a>
                </div>

                <div class="card-body">
                    <div class="mb-3">
                        <strong>Name:</strong>
                        <p class="form-control-plaintext">{{ $user->name }}</p>
                    </div>

                    <div class="mb-3">
                        <strong>Email:</strong>
                        <p class="form-control-plaintext">{{ $user->email }}</p>
                    </div>

                    <div class="mb-3">
                        <strong>Role:</strong>
                        <span class="badge bg-info text-dark">{{ ucfirst($user->role) }}</span>
                    </div>

                    <div class="mb-3">
                        <strong>Credit:</strong>
                        <span class="badge bg-success">${{ number_format($user->credit, 2) }}</span>
                    </div>

                    <div class="mt-4 d-flex justify-content-end">
                        <a href="{{ route('profile.edit') }}" class="btn btn-outline-primary me-2">Edit Profile</a>
                        <a href="{{ route('password.change') }}" class="btn btn-outline-warning">Change Password</a>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>
@endsection
