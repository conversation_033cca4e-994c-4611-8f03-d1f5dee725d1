<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class ManagerSeeder extends Seeder
{
    public function run(): void
    {
        User::create([
            'name' => 'Manager User',
            'email' => '<EMAIL>',
            'password' => Hash::make('manager123'),
            'role' => 'manager',
            'credit' => 5000,
        ]);
    }
}
