@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row">
        <div class="col-md-12">
            <h1 class="mb-4">Manage Drivers</h1>
            
            @if(session('success'))
                <div class="alert alert-success">
                    {{ session('success') }}
                </div>
            @endif

            <div class="mb-3">
                <a href="{{ route('manager.create-driver') }}" class="btn btn-success">
                    <i class="fas fa-plus"></i> Add New Driver
                </a>
                <a href="{{ route('manager.dashboard') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5>Drivers List</h5>
                </div>
                <div class="card-body">
                    @if($drivers->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Credit</th>
                                        <th>Created At</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($drivers as $driver)
                                        <tr>
                                            <td>{{ $driver->id }}</td>
                                            <td>{{ $driver->name }}</td>
                                            <td>{{ $driver->email }}</td>
                                            <td>${{ number_format($driver->credit, 2) }}</td>
                                            <td>{{ $driver->created_at->format('Y-m-d') }}</td>
                                            <td>
                                                <span class="badge bg-success">Driver</span>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        
                        {{ $drivers->links() }}
                    @else
                        <div class="text-center py-4">
                            <p class="text-muted">No drivers found.</p>
                            <a href="{{ route('manager.create-driver') }}" class="btn btn-primary">
                                Add First Driver
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
