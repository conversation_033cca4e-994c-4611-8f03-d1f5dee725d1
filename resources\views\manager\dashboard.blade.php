@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row">
        <div class="col-md-12">
            <h1 class="mb-4">Manager Dashboard</h1>
            <p class="lead">Welcome, {{ auth()->user()->name }}!</p>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ $totalEmployees }}</h4>
                            <p>Total Employees</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{{ route('manager.employees') }}" class="text-white">
                        View Details <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ $totalDrivers }}</h4>
                            <p>Total Drivers</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-truck fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{{ route('manager.drivers') }}" class="text-white">
                        View Details <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ $totalCustomers }}</h4>
                            <p>Total Customers</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-friends fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{{ route('customers.index') }}" class="text-white">
                        View Details <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Quick Actions</h5>
                </div>
                <div class="card-body">
                    <a href="{{ route('manager.create-employee') }}" class="btn btn-primary mb-2 d-block">
                        <i class="fas fa-plus"></i> Add New Employee
                    </a>
                    <a href="{{ route('manager.create-driver') }}" class="btn btn-success mb-2 d-block">
                        <i class="fas fa-plus"></i> Add New Driver
                    </a>
                    <a href="{{ route('products.index') }}" class="btn btn-info d-block">
                        <i class="fas fa-box"></i> View Products
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
