<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Product extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'description', 'price', 'stock', 'image'];

    protected $casts = ['price' => 'decimal:2'];

    public function purchases()
    {
        return $this->hasMany(Purchase::class);
    }
    protected static function boot()
{
    parent::boot();

    static::updating(function ($product) {
        if ($product->stock <= -1) {
            Notification::send(User::where('role', 'admin')->get(), new LowStockNotification($product));
        }
    });
}

}
