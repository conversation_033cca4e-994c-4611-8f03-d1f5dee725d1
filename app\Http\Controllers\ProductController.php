<?php

namespace App\Http\Controllers;

use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use App\Models\Purchase;

class ProductController extends Controller {
    public function index(Request $request)
{
    $search = $request->input('search');

    $products = Product::when($search, function ($query, $search) {
        return $query->where('name', 'like', "%{$search}%")
                     ->orWhere('description', 'like', "%{$search}%");
    })->paginate(6);

    return view('products.index', compact('products'));
}



    public function create() {
        return view('products.create');
    }

    public function store(Request $request)
{
    $request->validate([
        'name' => 'required|string|max:255',
        'description' => 'nullable|string',
        'price' => 'required|numeric',
        'stock' => 'required|integer',
        'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
    ]);

    $imagePath = null;
    if ($request->hasFile('image')) {
        $imagePath = $request->file('image')->store('product_images', 'public');
    }

    Product::create([
        'name' => $request->name,
        'description' => $request->description,
        'price' => $request->price,
        'stock' => $request->stock,
        'image' => $imagePath,
    ]);

    return redirect()->route('products.index')->with('success', 'Product added successfully.');
}


    public function edit(Product $product)
{
    return view('products.edit', compact('product'));
}
public function update(Request $request, Product $product)
{
    $request->validate([
        'name' => 'required|string|max:255',
        'price' => 'required|numeric|min:1',
        'stock' => 'required|integer|min:0',
        'description' => 'nullable|string'
    ]);

    $product->update($request->all());
    return redirect()->route('products.index')->with('success', 'Product updated successfully!');
}
public function show($id)
{
    $product = Product::findOrFail($id);
    return view('products.show', compact('product'));
}

public function destroy($id)
{
    $product = Product::findOrFail($id);

    if ($product->image) {
        Storage::disk('public')->delete($product->image);
    }

    $product->delete();

    return redirect()->route('products.index')->with('success', 'Product deleted successfully.');
}
public function buy($id)
{
    $user = auth()->user();

    if (strtolower($user->role) !== 'customer') {
        return redirect()->back()->with('error', 'Only customers can make purchases.');
    }

    $product = Product::findOrFail($id);

    if ($product->stock <= 0) {
        return redirect()->back()->with('error', 'Product is out of stock.');
    }

    if ($user->credit < $product->price) {
        return redirect()->back()->with('error', 'Insufficient credit.');
    }

    $user->credit -= $product->price;
    $user->save();

    $product->stock -= 1;
    $product->save();

    \App\Models\Purchase::create([
        'user_id' => $user->id,
        'product_id' => $product->id,
        'price' => $product->price,
    ]);

    return redirect()->back()->with('success', 'Product purchased successfully.');
}
}