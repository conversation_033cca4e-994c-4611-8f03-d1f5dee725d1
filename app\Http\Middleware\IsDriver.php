<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class IsDriver
{
    public function handle(Request $request, Closure $next)
    {
        if (auth()->check() && (auth()->user()->role == 'driver' || auth()->user()->role == 'admin' || auth()->user()->role == 'manager')) {
            return $next($request);
        }
        abort(403, 'Unauthorized action.');
    }
}
