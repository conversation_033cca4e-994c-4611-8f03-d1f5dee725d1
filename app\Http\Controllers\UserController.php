<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;




class UserController extends Controller
{
    public function index(Request $request)
{
    $query = User::query();

    if ($request->has('search') && $request->search != '') {
        $query->where(function ($q) use ($request) {
            $q->where('name', 'like', '%' . $request->search . '%')
              ->orWhere('email', 'like', '%' . $request->search . '%');
              
        });
    }

    if ($request->has('role') && $request->role != '') {
        $query->where('role', $request->role);
    }

    $users = $query->paginate(10);

    return view('users.index', compact('users'));
}

public function create()
{
    return view('users.create');
}


    public function edit(User $user)
{
    
    if (auth()->user()->role === 'admin' && $user->role === 'admin' && auth()->id() !== $user->id) {
        return redirect()->route('users.index')->with('error', '<PERSON><PERSON> cannot edit other admins.');
    }

    return view('users.edit', compact('user'));
}


public function update(Request $request, User $user)
{
    
    if (auth()->user()->role === 'admin' && $user->role === 'admin' && auth()->id() !== $user->id) {
        return redirect()->route('users.index')->with('error', 'Admins cannot edit other admins.');
    }

    $validated = $request->validate([
        'name' => 'required|string|max:255',
        'email' => 'required|email|unique:users,email,' . $user->id,
    ]);

    $user->update($validated);
    return redirect()->route('users.index')->with('success', 'User updated successfully.');
}


public function updatePassword($id)
{
    $user = User::findOrFail($id);
    $user->password = bcrypt('password123'); 
    $user->save();

    return redirect()->route('users.index')->with('success', 'Password reset successfully!');
}


    public function destroy($id)
{
    $user = User::findOrFail($id);

    
    if ($user->role === 'admin') {
        return redirect()->route('users.index')->with('error', 'You cannot delete an admin user.');
    }

    $user->delete();
    return redirect()->route('users.index')->with('success', 'User deleted successfully!');
}
public function manageCredit($id)
{
    $user = User::findOrFail($id);
    return view('users.manage_credit', compact('user'));
}

public function updateCredit(Request $request, $id)
{
    $request->validate([
        'credit' => 'required|numeric',
    ]);

    $user = User::findOrFail($id);
    $user->credit += $request->credit;
    $user->save();

    return redirect()->route('users.index')->with('success', 'User credit updated successfully!');
}
public function showProfile()
{
    return view('profile'); 
}

public function show($id)
{
    $user = User::findOrFail($id);
    return view('users.profile', compact('user'));
}
public function changePasswordForm()
{
    return view('users.change_password');
}

public function changePassword(Request $request)
{
    $request->validate([
        'current_password' => 'required',
        'new_password' => 'required|string|min:6|confirmed',
    ]);

    $user = auth()->user();

    if (!Hash::check($request->current_password, $user->password)) {
        return back()->withErrors(['current_password' => 'Current password is incorrect']);
    }

    $user->password = Hash::make($request->new_password);
    $user->save();

    return redirect()->route('password.change')->with('success', 'Password changed successfully.');
}
public function store(Request $request)
{
    $request->validate([
        'name' => 'required|string|max:255',
        'email' => 'required|email|unique:users',
        'password' => 'required|min:6',
        'role' => 'required|in:customer,employee'
    ]);

    User::create([
        'name' => $request->name,
        'email' => $request->email,
        'password' => Hash::make($request->password),
        'role' => $request->role,
        'credit' => 0
    ]);

    return redirect()->route('users.index')->with('success', 'User created successfully!');
}
}
