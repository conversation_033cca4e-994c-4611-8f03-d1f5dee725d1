<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class AdminController extends Controller
{
    // عرض نموذج إضافة يوزر جديد باستخدام نفس create العادي
    public function createUser()
    {
        return view('users.create'); // بيستخدم الملف العام اللي عندك
    }

    // تخزين المستخدم الجديد
    public function storeUser(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users',
            'password' => 'required|string|min:6|confirmed',
            'role' => 'required|in:employee,customer',
        ]);

        User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => $request->role,
            'credit' => 0,
        ]);

        return redirect()->route('users.index')->with('success', 'User created successfully.');
    }
}
