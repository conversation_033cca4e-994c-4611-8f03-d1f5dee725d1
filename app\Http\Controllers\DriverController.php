<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Product;
use Illuminate\Http\Request;

class DriverController extends Controller
{
    // عرض dashboard السائق
    public function dashboard()
    {
        $driver = auth()->user();
        $totalDeliveries = 0; // يمكن إضافة جدول deliveries لاحقاً
        $pendingDeliveries = 0; // يمكن إضافة جدول deliveries لاحقاً
        
        return view('driver.dashboard', compact('driver', 'totalDeliveries', 'pendingDeliveries'));
    }

    // عرض المهام المتاحة
    public function tasks()
    {
        // في المستقبل يمكن إضافة جدول للمهام/التوصيلات
        // حالياً سنعرض المنتجات كمثال
        $products = Product::latest()->paginate(10);
        return view('driver.tasks', compact('products'));
    }

    // عرض تفاصيل مهمة
    public function taskDetails($id)
    {
        $product = Product::findOrFail($id);
        return view('driver.task-details', compact('product'));
    }

    // تحديث حالة المهمة
    public function updateTaskStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|in:pending,in_progress,completed'
        ]);

        // في المستقبل يمكن تحديث حالة المهمة في جدول deliveries
        // حالياً سنعرض رسالة نجاح فقط
        
        return redirect()->route('driver.tasks')->with('success', 'Task status updated successfully.');
    }

    // عرض الملف الشخصي
    public function profile()
    {
        $driver = auth()->user();
        return view('driver.profile', compact('driver'));
    }
}
