<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class ManagerController extends Controller
{
    // عرض dashboard المدير
    public function dashboard()
    {
        $totalEmployees = User::where('role', 'employee')->count();
        $totalDrivers = User::where('role', 'driver')->count();
        $totalCustomers = User::where('role', 'customer')->count();
        
        return view('manager.dashboard', compact('totalEmployees', 'totalDrivers', 'totalCustomers'));
    }

    // عرض قائمة الموظفين
    public function employees()
    {
        $employees = User::where('role', 'employee')->paginate(10);
        return view('manager.employees', compact('employees'));
    }

    // عرض قائمة السائقين
    public function drivers()
    {
        $drivers = User::where('role', 'driver')->paginate(10);
        return view('manager.drivers', compact('drivers'));
    }

    // إنشاء موظف جديد
    public function createEmployee()
    {
        return view('manager.create-employee');
    }

    // حفظ موظف جديد
    public function storeEmployee(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users',
            'password' => 'required|string|min:6|confirmed',
        ]);

        User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => 'employee',
            'credit' => 0,
        ]);

        return redirect()->route('manager.employees')->with('success', 'Employee created successfully.');
    }

    // إنشاء سائق جديد
    public function createDriver()
    {
        return view('manager.create-driver');
    }

    // حفظ سائق جديد
    public function storeDriver(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users',
            'password' => 'required|string|min:6|confirmed',
        ]);

        User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => 'driver',
            'credit' => 0,
        ]);

        return redirect()->route('manager.drivers')->with('success', 'Driver created successfully.');
    }
}
