@extends('layouts.app')

@section('content')
<div class="container">
    <h2 class="mb-4">My Purchases</h2>

    @if($purchases && count($purchases) > 0)
        <div class="row">
            @foreach($purchases as $purchase)
                @php
                    $product = $purchase->product;
                @endphp

                @if($product)
                    <div class="col-md-4 mb-4">
                        <div class="card shadow-sm">
                            <img src="{{ $product->image ? asset('storage/' . $product->image) : asset('default-product.jpg') }}" 
                                 class="card-img-top" style="height: 200px; object-fit: cover;" alt="Product Image">

                            <div class="card-body">
                                <h5 class="card-title">{{ $product->name }}</h5>
                                <p class="card-text">{{ $product->description }}</p>
                                <p><strong>Price:</strong> ${{ $purchase->price }}</p>

                                {{-- Return Product --}}
                                <form action="{{ route('purchases.return', $purchase->id) }}" method="POST" onsubmit="return confirm('Are you sure you want to return this product?');">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-danger btn-sm mt-2 w-100">Return Product</button>
                                </form>
                            </div>
                        </div>
                    </div>
                @endif
            @endforeach
        </div>
    @else
        <p>No purchases found.</p>
    @endif
</div>
@endsection
