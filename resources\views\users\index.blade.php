@extends('layouts.app')

@section('content')
<div class="container">
    <h2 class="mb-4">Manage Users</h2>

    @if(session('success'))
        <div class="alert alert-success">{{ session('success') }}</div>
    @endif
    @if(session('error'))
        <div class="alert alert-danger">{{ session('error') }}</div>
    @endif

    <a href="{{ route('users.create') }}" class="btn btn-success mb-3">Add New User</a>

    <form action="{{ route('users.index') }}" method="GET" class="mb-3 d-flex">
        <input type="text" name="search" class="form-control me-2" placeholder="Search users..." value="{{ request('search') }}">
        <select name="role" class="form-select me-2">
            <option value="">All Roles</option>
            <option value="admin" {{ request('role') == 'admin' ? 'selected' : '' }}>Admin</option>
            <option value="employee" {{ request('role') == 'employee' ? 'selected' : '' }}>Employee</option>
            <option value="customer" {{ request('role') == 'customer' ? 'selected' : '' }}>Customer</option>
        </select>
        <button type="submit" class="btn btn-primary">Search</button>
    </form>

    <table class="table table-striped table-bordered">
        <thead class="table-dark">
            <tr>
                <th>ID</th>
                <th>Name</th>
                <th>Email</th>
                <th>Role</th>
                <th>Credit</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach($users as $user)
            <tr>
                <td>{{ $user->id }}</td>
                <td>{{ $user->name }}</td>
                <td>{{ $user->email }}</td>
                <td>
                    <span class="badge bg-{{ $user->role == 'admin' ? 'danger' : ($user->role == 'employee' ? 'warning' : 'success') }}">
                        {{ ucfirst($user->role) }}
                    </span>
                </td>
                <td>${{ number_format($user->credit, 2) }}</td>
                <td>
                    @if(auth()->user()->role === 'admin')
                        <a href="{{ route('users.edit', $user->id) }}" class="btn btn-sm btn-primary">Edit</a>

                        <form action="{{ route('users.updatePassword', $user->id) }}" method="POST" class="d-inline">
                            @csrf
                            <button type="submit" class="btn btn-sm btn-warning">Reset Password</button>
                        </form>

                        <a href="{{ route('users.manageCredit', $user->id) }}" class="btn btn-sm btn-info">Manage Credit</a>

                        @if($user->role !== 'admin')  
                        <form action="{{ route('users.destroy', $user->id) }}" method="POST" class="d-inline">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure?')">Delete</button>
                        </form>
                        @endif
                    @endif
                </td>
            </tr>
            @endforeach
        </tbody>
    </table>

    {{ $users->appends(request()->query())->links() }}
</div>
@endsection
