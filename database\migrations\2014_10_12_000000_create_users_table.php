<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
{
    Schema::create('users', function (Blueprint $table) {
        $table->id();
        $table->string('name');
        $table->string('email')->unique();
        $table->string('password');
        $table->enum('role', ['admin', 'employee', 'customer'])->default('customer');
        $table->decimal('credit', 10, 2)->default(0);
        $table->timestamps();
    });
}


    public function down()
    {
        Schema::dropIfExists('users');
    }
};
