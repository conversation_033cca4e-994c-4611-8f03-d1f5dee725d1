<?php

use App\Http\Controllers\AuthController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\PurchaseController;
use App\Http\Controllers\CreditController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\RegisterController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\AdminController;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Auth\AuthenticatedSessionController;
Route::middleware(['auth'])->group(function () {
    Route::get('/users', [AdminController::class, 'index']);
    Route::get('/users', [UserController::class, 'index'])->name('users.index');
});
;
Route::delete('/purchases/{purchase}/return', [\App\Http\Controllers\PurchaseController::class, 'return'])->name('purchases.return');

Route::middleware(['auth', 'isAdmin'])->group(function () {
    Route::get('/users/create', [UserController::class, 'create'])->name('users.create');
});
// Routes للإدمن
Route::get('/users/create', [AdminController::class, 'createUser'])->name('users.create');
Route::post('/users', [AdminController::class, 'storeUser'])->name('users.store');

Route::post('/logout', [AuthenticatedSessionController::class, 'destroy'])->name('logout');
Route::post('/products/{id}/buy', [ProductController::class, 'buy'])->name('products.buy');
Route::post('/products/{product}/buy', [ProductController::class, 'buy'])->name('products.buy');
Route::post('/products/{product}/buy', [ProductController::class, 'buy'])->name('products.buy')->middleware('auth');

Route::post('/logout', function () {
    Auth::logout();
    return redirect('/login');
})->name('logout');
Route::get('/profile', [UserController::class, 'showProfile'])->name('profile.show');
Route::delete('/products/{id}/delete', [ProductController::class, 'destroy'])->name('products.destroy');
Route::get('/purchases', [App\Http\Controllers\PurchaseController::class, 'index'])->name('purchases.index');
Route::get('/profile/edit', [App\Http\Controllers\UserController::class, 'edit'])->name('profile.edit');
Route::post('/profile/update', [App\Http\Controllers\UserController::class, 'update'])->name('profile.update');
Route::get('/profile/change-password', [App\Http\Controllers\UserController::class, 'changePasswordForm'])->name('password.change');
Route::post('/profile/change-password', [App\Http\Controllers\UserController::class, 'changePassword'])->name('password.update');
Route::put('/users/{user}/password', [UserController::class, 'updatePassword'])->name('users.updatePassword');

Route::resource('products', ProductController::class);

Route::resource('products', ProductController::class);
Route::resource('users', UserController::class);
Route::delete('/users/{id}', [UserController::class, 'destroy'])->name('users.destroy');
Route::post('/users/{id}/update-password', [UserController::class, 'updatePassword'])->name('users.updatePassword');
Route::delete('/users/{id}/delete', [UserController::class, 'destroy'])->name('users.destroy');
Route::put('/users/{user}/update-password', [UserController::class, 'updatePassword'])->name('users.updatePassword');
Route::get('/users/{id}/manage-credit', [UserController::class, 'manageCredit'])->name('users.manageCredit');
Route::post('/users/{id}/update-credit', [UserController::class, 'updateCredit'])->name('users.updateCredit');

Route::middleware(['auth', 'admin'])->group(function () {
    Route::get('/admin/users', [AdminController::class, 'index'])->name('admin.users.index');
    Route::get('/admin/users/create', [AdminController::class, 'create'])->name('admin.users.create');
    Route::post('/admin/users', [AdminController::class, 'store'])->name('admin.users.store');
});
Route::get('/products/{id}', [ProductController::class, 'show'])->name('products.show');

use App\Http\Controllers\CustomerController;
Route::middleware(['auth', 'isAdmin'])->group(function () {
    Route::get('/users', [UserController::class, 'index'])->name('users.index');
});
Route::middleware(['auth', 'isEmployee'])->group(function () {
    Route::get('/customers', [CustomerController::class, 'index'])->name('customers.index');
    Route::post('/customers/{user}/add-credit', [CustomerController::class, 'addCredit'])->name('customers.addCredit');
});

Route::middleware(['auth', 'isAdmin'])->group(function () {
    Route::get('/users', [UserController::class, 'index'])->name('users.index');
    Route::get('/users/create', [UserController::class, 'create'])->name('users.create');
    Route::post('/users', [UserController::class, 'store'])->name('users.store');
    Route::get('/users/{user}/edit', [UserController::class, 'edit'])->name('users.edit');
    Route::post('/users/{user}', [UserController::class, 'update'])->name('users.update');
    Route::post('/users/{user}/delete', [UserController::class, 'destroy'])->name('users.destroy');
});

Route::middleware(['auth'])->group(function () {
    Route::get('/products', [ProductController::class, 'index'])->name('products.index');
    Route::post('/products/{product}/buy', [ProductController::class, 'buy'])->name('products.buy');
});

Route::middleware(['auth', 'isEmployee'])->group(function () {
    Route::get('/products/create', [ProductController::class, 'create'])->name('products.create');
    Route::post('/products', [ProductController::class, 'store'])->name('products.store');
    Route::get('/products/{product}/edit', [ProductController::class, 'edit'])->name('products.edit'); // ✅ مسار التعديل
    Route::post('/products/{product}', [ProductController::class, 'update'])->name('products.update');
    Route::post('/products/{product}/delete', [ProductController::class, 'destroy'])->name('products.destroy');
});

Route::middleware(['auth', 'isAdmin'])->group(function () {
    Route::get('/users', [UserController::class, 'index'])->name('users.index');
    Route::get('/users/{user}/edit', [UserController::class, 'edit'])->name('users.edit');
    Route::post('/users/{user}', [UserController::class, 'update'])->name('users.update');
    Route::post('/users/{user}/delete', [UserController::class, 'destroy'])->name('users.destroy');
    Route::post('/users/{user}/reset-password', [UserController::class, 'resetPassword'])->name('users.resetPassword');
});

Route::get('/register', [RegisterController::class, 'showRegister'])->name('register');
Route::post('/register', [RegisterController::class, 'register']);

Route::middleware(['auth'])->group(function () {
    Route::get('/profile', [ProfileController::class, 'index'])->name('profile');
});

Route::get('/', [HomeController::class, 'index'])->name('home');

// Authentication Routes
Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
Route::post('/login', [AuthController::class, 'login']);
Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

// Products Routes
Route::middleware(['auth'])->group(function () {
    Route::get('/products', [ProductController::class, 'index'])->name('products.index');
    Route::post('/products/{product}/buy', [PurchaseController::class, 'buy'])->name('products.buy');
});

// Employees & Admin Routes
Route::middleware(['auth', 'isEmployee'])->group(function () {
    Route::get('/products/create', [ProductController::class, 'create'])->name('products.create');
    Route::post('/products', [ProductController::class, 'store'])->name('products.store');
    Route::post('/customers/{user}/add-credit', [CreditController::class, 'addCredit'])->name('customers.addCredit');
});