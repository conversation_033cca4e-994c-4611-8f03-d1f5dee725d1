<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class DriverSeeder extends Seeder
{
    public function run(): void
    {
        User::create([
            'name' => 'Driver User',
            'email' => '<EMAIL>',
            'password' => Hash::make('driver123'),
            'role' => 'driver',
            'credit' => 1000,
        ]);
    }
}
