@extends('layouts.app')

@section('content')
<div class="container">
    <h1 class="mb-4">Product List</h1>

    {{-- Search --}}
    <form action="{{ route('products.index') }}" method="GET" class="mb-4">
        <div class="input-group">
            <input type="text" name="search" class="form-control" placeholder="Search products..." value="{{ request('search') }}">
            <button type="submit" class="btn btn-primary">Search</button>
        </div>
    </form>

    {{-- Add Product (for Admin or Employee) --}}
    @auth
        @if(in_array(strtolower(auth()->user()->role), ['admin', 'employee']))
            <div class="mb-4">
                <a href="{{ route('products.create') }}" class="btn btn-success">Add Product</a>
            </div>
        @endif
    @endauth

    {{-- Product Cards --}}
    <div class="row">
        @foreach($products as $product)
            <div class="col-md-4">
                <div class="card mb-4 shadow-sm">
                    <img src="{{ $product->image ? asset('storage/' . $product->image) : asset('default-product.jpg') }}" 
                         class="card-img-top product-image" alt="Product Image">
                    
                    <div class="card-body">
                        <h5 class="card-title">{{ $product->name }}</h5>
                        <p class="card-text">{{ $product->description }}</p>
                        <p><strong>Price:</strong> ${{ number_format($product->price, 2) }}</p>
                        <p><strong>Stock:</strong> {{ $product->stock }}</p>

                        {{-- Buy Button for Customers --}}
                        @if($product->stock > 0 && auth()->user()->credit >= $product->price)
    <form action="{{ route('products.buy', $product->id) }}" method="POST">
        @csrf
        <button type="submit" class="btn btn-success btn-sm">Buy</button>
    </form>
@elseif($product->stock <= 0)
    <button class="btn btn-secondary w-100" disabled>Out of Stock</button>
@else
    <button class="btn btn-warning w-100" disabled>Insufficient Credit</button>
@endif


                        {{-- Edit/Delete Buttons for Employee/Admin --}}
                        @if(auth()->check() && in_array(strtolower(auth()->user()->role), ['admin', 'employee']))
                            <div class="mt-2 d-flex justify-content-between">
                                <a href="{{ route('products.edit', $product->id) }}" class="btn btn-warning btn-sm">Edit</a>
                                
                                <form action="{{ route('products.destroy', $product->id) }}" method="POST" onsubmit="return confirm('Are you sure?');">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-danger btn-sm">Delete</button>
                                </form>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        @endforeach
    </div>

    {{-- Pagination --}}
    <div class="d-flex justify-content-center mt-4">
        {{ $products->links() }}
    </div>
</div>

<style>
    .product-image {
        height: 250px; 
        object-fit: cover;
    }
</style>

@endsection
